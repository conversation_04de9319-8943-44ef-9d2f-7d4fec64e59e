@import 'tailwindcss';
@import 'tw-animate-css';

@plugin '../../hero.ts';
/* Note: You may need to change the path to fit your project structure */
@source '../../node_modules/@heroui/theme/dist/**/*.{js,ts,jsx,tsx}';
@config "../../tailwind.config.js";

@custom-variant dark (&:is(.dark *));

@theme {
  --font-family-sans: 'Inter', ui-sans-serif, system-ui, sans-serif;
  --font-family-poppins: 'Poppins', ui-sans-serif, system-ui, sans-serif;

  --container-padding: 2rem;
  --container-max-width-2xl: 1400px;
  --color-border: oklch(88% 0.03 260);
  --color-input: oklch(88% 0.03 260);
  --color-ring: oklch(65% 0.2 270);
  --color-background: oklch(100% 0 0);
  --color-foreground: oklch(25% 0.15 260);
  --color-accent: oklch(80% 0.15 60);
  --color-accent-foreground: oklch(25% 0.15 260);
  --color-muted: oklch(90% 0.02 260);
  --color-muted-foreground: oklch(60% 0.05 260);
  --color-card: oklch(98% 0.01 260);
  --color-card-foreground: oklch(25% 0.15 260);
}

@layer base {
  :root {
    --background: oklch(100% 0 0);
    --foreground: oklch(25% 0.15 260);
    --card: oklch(98% 0.01 260);
    --card-foreground: oklch(25% 0.15 260);
    --popover: oklch(98% 0.01 260);
    --popover-foreground: oklch(25% 0.15 260);
    --muted: oklch(90% 0.02 260);
    --muted-foreground: oklch(60% 0.05 260);
    --accent: oklch(80% 0.15 60);
    --accent-foreground: oklch(25% 0.15 260);
    --border: oklch(88% 0.03 260);
    --input: oklch(88% 0.03 260);
    --ring: oklch(65% 0.2 270);
  }

  .dark {
    --background: oklch(15% 0.05 260);
    --foreground: oklch(90% 0.02 260);
    --card: oklch(20% 0.05 260);
    --card-foreground: oklch(90% 0.02 260);
    --popover: oklch(20% 0.05 260);
    --popover-foreground: oklch(90% 0.02 260);
    --muted: oklch(30% 0.04 260);
    --muted-foreground: oklch(65% 0.05 260);
    --accent: oklch(75% 0.15 60);
    --accent-foreground: oklch(90% 0.02 260);
    --border: oklch(30% 0.04 260);
    --input: oklch(30% 0.04 260);
    --ring: oklch(30% 0.05 260);
  }
}

@layer base {
  * {
    border-color: var(--border);
  }
  body {
    background-color: var(--background);
    color: var(--foreground);
    @apply overflow-x-hidden max-w-full;
  }
  html {
    @apply overflow-x-hidden max-w-full;
  }
}

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

.btn-gradient {
  background-image:
    linear-gradient(0deg, #1c1c1c, #1c1c1c),
    linear-gradient(90deg, #ec0fac 0%, #6b30ea 100%);
  background-blend-mode: overlay;
  color: white;
}

.text-gradient {
  background: linear-gradient(90deg, #ec0fac 0%, #6b30ea 100%);

  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.text-gradient1 {
  background: linear-gradient(
    222.97deg,
    #f2a1f2 -49.04%,
    #a195f9 -4.67%,
    #707ff5 39.7%,
    #4b4bc3 84.07%,
    #1e1e76 128.44%
  );

  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* Custom scrollbar styles */
.custom-blur {
  backdrop-filter: blur(23.484766006469727px);
  box-shadow: inset 0px 1px 6.17px rgba(255, 255, 255, 0.1);
}

.hide-scrollbar {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* Internet Explorer 10+ */
}

.hide-scrollbar::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

.embla__buttons {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.6rem;
  align-items: center;
}

.gradient-border {
  border-bottom: 3px solid;
  border-image:
    linear-gradient(0deg, #1c1c1c, #1c1c1c),
    linear-gradient(90deg, #ec0fac 0%, #6b30ea 100%);
  border-image: 1;
}
