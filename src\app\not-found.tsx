'use client';

import { motion } from 'framer-motion';
import { ArrowLeft, Home } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

const NotFoundContent = () => {
  const router = useRouter();
  const [mounted, setMounted] = useState(false);

  // Prevent hydration mismatch
  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) return null;

  return (
    <div className="min-h-screen w-full flex flex-col items-center justify-center px-4 bg-[var(--background)] text-[var(--foreground)] overflow-hidden relative">
      {/* Animated background particles */}
      <div className="absolute inset-0 pointer-events-none">
        {[...Array(8)].map((_, i) => (
          <motion.div
            // eslint-disable-next-line react/no-array-index-key
            key={`particle-${i}`}
            className="absolute rounded-full bg-[var(--primary)]/[0.15]"
            style={{
              width: `${Math.random() * 50 + 20}px`,
              height: `${Math.random() * 50 + 20}px`,
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              scale: [1, 1.5, 1],
              opacity: [0.3, 0.5, 0.3],
              x: [0, Math.random() * 100 - 50],
              y: [0, Math.random() * 100 - 50],
            }}
            transition={{
              duration: Math.random() * 20 + 15,
              repeat: Infinity,
              repeatType: 'loop',
            }}
          />
        ))}
      </div>

      {/* Main content */}
      <motion.div
        className="relative z-10 text-center space-y-8"
        initial={{ opacity: 0, y: 50 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
      >
        {/* Error Message */}
        <motion.div
          className="space-y-4"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.3, duration: 0.6 }}
        >
          <h2 className="text-2xl md:text-3xl font-semibold text-[var(--foreground)]">
            404, Page Not Found
          </h2>
          <p className="text-[var(--muted-foreground)] max-w-md mx-auto">
            The page you&apos;re looking for seems to have vanished. Let&apos;s
            get you back on track!
          </p>
        </motion.div>

        {/* Action Buttons */}
        <motion.div
          className="flex flex-col sm:flex-row justify-center gap-4"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.7, duration: 0.6 }}
        >
          <button
            type="button"
            onClick={() => router.push('/')}
            className="inline-flex items-center justify-center h-12 px-6 rounded-full bg-[var(--primary)] text-[var(--primary-foreground)] font-semibold shadow-lg hover:bg-[var(--primary)]/[0.9] transition-all duration-300 hover:scale-105"
          >
            <Home className="h-5 w-5 mr-2" />
            Back to Home
          </button>
          <button
            type="button"
            onClick={() => router.back()}
            className="inline-flex items-center justify-center h-12 px-6 rounded-full border border-[var(--border)] bg-[var(--card)] text-[var(--foreground)] font-semibold shadow-sm hover:bg-[var(--accent)] hover:text-[var(--accent-foreground)] transition-all duration-300 hover:scale-105"
          >
            <ArrowLeft className="h-5 w-5 mr-2" />
            Go Back
          </button>
        </motion.div>
      </motion.div>
    </div>
  );
};

const NotFound = ({ error }: any) => {
  useEffect(() => {
    if (error) {
      // Log error for debugging
      // console.error(error);
    }
  }, [error]);

  return <NotFoundContent />;
};

export default NotFound;
