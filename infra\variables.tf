
variable "project_id" {
  type = string
}

variable "region" {
  type = string
  default = "europe-west2"
}

variable "image_tag" {
  type = string
  default = "development-42"
}

variable "CPU_REQUEST" {
  type = string
  default = "512m"
}

variable "CPU_LIMIT" {
  type = string
  default = "1024m"
}

variable "MEM_REQUEST" {
  type = string
  default = "512Mi"
}

variable "MEM_LIMIT" {
  type = string
  default = "1024Mi"
}

variable "SERVICE_NAME" {
  type = string
  default = "nxvoy-travel-agent-fer2"
}

variable "SERVICE_PORT" {
  type = string
  default = "80"
}

variable "NODE_PORT" {
  type = string
  default = "30081"
}

variable "HEALTH_CHECK_PATH" {
  type = string
  default = "/"
}

variable "CONTAINER_REGISTRY" {
  type = string
  default = ""
}