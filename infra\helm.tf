resource "helm_release" "service" {
  name       = kubernetes_namespace.service.metadata[0].name
  chart      = "./helm"
  namespace  = kubernetes_namespace.service.metadata[0].name
  timeout    = "600"

  values = [templatefile("values/default-values.yaml", {
    image_tag = var.image_tag,
    dns = local.dns_name,
    cpu_request = local.cpu_request,
    cpu_limit = local.cpu_limit,
    mem_request = local.mem_request,
    mem_limit = local.mem_limit,  
    replica = local.replicas, 
    max_replica = local.max_replicas 
    service_name = var.SERVICE_NAME,
    service_port = var.SERVICE_PORT,
    node_port = var.NODE_PORT,
    health_check_path = var.HEALTH_CHECK_PATH,
    container_registry = var.CONTAINER_REGISTRY,
    env = local.environment,
  })]  
}