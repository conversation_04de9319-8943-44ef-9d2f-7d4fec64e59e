# Color Reference Guide

This document shows all available colors in your Tailwind configuration and how to use them.

## Available Colors

### Primary Color (#1E1E76)
```jsx
// Text colors
<p className="text-primary">Primary text</p>
<p className="text-primary-foreground">Primary foreground text</p>

// Background colors
<div className="bg-primary">Primary background</div>
<div className="bg-primary-200">Primary 200 background (#707FF5)</div>

// Combined usage
<button className="bg-primary text-primary-foreground">Primary Button</button>
```

### Secondary Color (#F2F2FF)
```jsx
// Text colors
<p className="text-secondary">Secondary text</p>
<p className="text-secondary-foreground">Secondary foreground text</p>

// Background colors
<div className="bg-secondary">Secondary background</div>

// Combined usage
<div className="bg-secondary text-secondary-foreground">Secondary container</div>
```

### Gray Color (#686C72)
```jsx
// Text colors
<p className="text-gray">Gray text</p>
<p className="text-gray-foreground">Gray foreground text</p>

// Background colors
<div className="bg-gray">Gray background</div>

// Combined usage
<div className="bg-gray text-gray-foreground">Gray container</div>
```

### Default Color Variants (#F5F5F5)
```jsx
// Available variants: default, 100, 200, 300, 400, 500, 600, 700, 800, 900, 1000

// Text colors
<p className="text-default">Default text</p>
<p className="text-default-100">Default 100 text</p>
<p className="text-default-500">Default 500 text</p>
<p className="text-default-900">Default 900 text</p>

// Background colors
<div className="bg-default">Default background</div>
<div className="bg-default-100">Default 100 background (#FFFFFF)</div>
<div className="bg-default-200">Default 200 background (#E8E8E8)</div>
<div className="bg-default-300">Default 300 background (#D2D2D2)</div>
<div className="bg-default-400">Default 400 background (#BBBBBB)</div>
<div className="bg-default-500">Default 500 background (#A4A4A4)</div>
<div className="bg-default-600">Default 600 background (#8E8E8E)</div>
<div className="bg-default-700">Default 700 background (#777777)</div>
<div className="bg-default-800">Default 800 background (#606060)</div>
<div className="bg-default-900">Default 900 background (#4A4A4A)</div>
<div className="bg-default-1000">Default 1000 background (#333333)</div>
```

### Red Color (#D00416)
```jsx
// Text colors
<p className="text-red">Red text</p>
<p className="text-red-foreground">Red foreground text</p>

// Background colors
<div className="bg-red">Red background</div>
<div className="bg-red-100">Red 100 background (#FB3748)</div>
<div className="bg-red-200">Red 200 background (#D00416)</div>

// Combined usage
<button className="bg-red text-red-foreground">Error Button</button>
```

### Yellow Color (#E4AE0D)
```jsx
// Text colors
<p className="text-yellow">Yellow text</p>
<p className="text-yellow-foreground">Yellow foreground text</p>

// Background colors
<div className="bg-yellow">Yellow background</div>
<div className="bg-yellow-100">Yellow 100 background (#F7C83B)</div>
<div className="bg-yellow-200">Yellow 200 background (#E4AE0D)</div>

// Combined usage
<div className="bg-yellow text-yellow-foreground">Warning container</div>
```

### Green Color (#1FC16B)
```jsx
// Text colors
<p className="text-green">Green text</p>
<p className="text-green-foreground">Green foreground text</p>

// Background colors
<div className="bg-green">Green background</div>
<div className="bg-green-100">Green 100 background (#84EBB4)</div>
<div className="bg-green-200">Green 200 background (#1FC16B)</div>

// Combined usage
<button className="bg-green text-green-foreground">Success Button</button>
```

### Light Gray Color (#8F9AA8)
```jsx
// Text colors
<p className="text-lightGray">Light gray text</p>
<p className="text-lightGray-foreground">Light gray foreground text</p>

// Background colors
<div className="bg-lightGray">Light gray background</div>

// Combined usage
<div className="bg-lightGray text-lightGray-foreground">Light gray container</div>
```

### Subtitle Color (#1C1C1C)
```jsx
// Text colors
<p className="text-subtitle">Subtitle text</p>
<p className="text-subtitle-foreground">Subtitle foreground text</p>

// Background colors
<div className="bg-subtitle">Subtitle background</div>

// Combined usage
<h3 className="text-subtitle">Section subtitle</h3>
<div className="bg-subtitle text-subtitle-foreground">Dark container</div>
```

## Common Usage Patterns

### Card Components
```jsx
<div className="bg-default-100 p-6 rounded-lg">
  <h2 className="text-primary font-bold text-xl mb-2">Card Title</h2>
  <p className="text-subtitle mb-4">Card subtitle</p>
  <p className="text-default-700">Card content</p>
  <button className="bg-primary text-primary-foreground px-4 py-2 rounded mt-4">
    Action Button
  </button>
</div>
```

### Navigation Items
```jsx
<nav className="bg-default-50">
  <a href="#" className="text-default-900 hover:text-primary">Home</a>
  <a href="#" className="text-subtitle hover:text-primary">About</a>
  <a href="#" className="text-lightGray hover:text-primary">Contact</a>
</nav>
```

### Status Indicators
```jsx
<span className="bg-green text-green-foreground px-2 py-1 rounded">Success</span>
<span className="bg-yellow text-yellow-foreground px-2 py-1 rounded">Warning</span>
<span className="bg-red text-red-foreground px-2 py-1 rounded">Error</span>
```

## Notes

- All colors include both text and background variants
- Foreground colors are automatically calculated for optimal contrast
- Use `-foreground` suffix for text that goes on colored backgrounds
- Default color has the most variants (100-1000) for fine-grained control
- Colors are defined in HeroUI theme configuration and work with dark mode
