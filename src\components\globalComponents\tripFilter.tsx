'use client';

import { useState } from 'react';
import { X } from 'lucide-react';
import { Input } from '@heroui/react';

import { GPSIcon, MagniferIcon, Pen2Icon, PointMapIcon } from '../icons';

export default function TripFilter() {
  const [startLocation, setStartLocation] = useState('Wales, United Kingdom');
  const [destinations, setDestinations] = useState<string[]>([
    'London',
    'Germany',
  ]);

  const handleRemove = (location: string) => {
    setDestinations(destinations.filter(d => d !== location));
  };

  return (
    <div className="w-full min-w-lg max-w-xl rounded-2xl bg-white p-6 space-y-6">
      {/* Start Location */}
      <div>
        <label className="block text-sm  text-secondary mb-1">
          Start location
        </label>
        <div className="relative">
          <Input
            labelPlacement="outside"
            placeholder="Enter location"
            endContent={<Pen2Icon />}
            value={startLocation}
            onChange={e => setStartLocation(e.target.value)}
            type="email"
            variant="bordered"
          />
        </div>
        <div className="mt-3 flex gap-3">
          <button
            type="button"
            className="flex items-center gap-2 font-medium rounded-md border border-primary-200 px-4 py-1.5 text-sm text-primary-200  transition"
          >
            <GPSIcon className="h-4 w-4" />
            Current Location
          </button>
          <button
            type="button"
            className="flex items-center gap-2 font-medium rounded-md border border-primary-200 px-4 py-1.5 text-sm text-primary-200  transition"
          >
            <PointMapIcon className="h-4 w-4" />
            Locate on Map
          </button>
        </div>
      </div>

      {/* Travel Destination */}
      <div>
        <label className="block text-sm text-secondary mb-1">
          Travel Destination
        </label>
        <div className="relative">
          <Input
            labelPlacement="outside"
            placeholder="Enter location"
            endContent={<MagniferIcon />}
            value={startLocation}
            onChange={e => setStartLocation(e.target.value)}
            type="email"
            variant="bordered"
          />
        </div>

        {/* Destination Tags */}
        <div className="mt-3 flex gap-2 flex-wrap">
          {destinations.map(loc => (
            <span
              key={loc}
              className="flex items-center gap-1 rounded-md bg-primary-200/10 text-primary-200 px-3 py-1 text-sm"
            >
              {loc}
              <button type="button" onClick={() => handleRemove(loc)}>
                <X className="h-3.5 w-3.5" />
              </button>
            </span>
          ))}
        </div>
      </div>
    </div>
  );
}
