import { heroui } from '@heroui/react';

/** @type {import('tailwindcss').Config} */
export default {
  content: [
    './app/**/*.{js,ts,jsx,tsx}',
    './components/**/*.{js,ts,jsx,tsx}',
    './src/**/*.{js,ts,jsx,tsx}',
  ],
  plugins: [
    heroui({
      themes: {
        light: {
          colors: {
            background: '#FFFFFF',
            foreground: '#11181C',
            primary: {
              foreground: '#FFFFFF',
              DEFAULT: '#1E1E76',
              200: '#707FF5',
            },
            secondary: {
              foreground: '#FFFFFF',
              DEFAULT: '#F2F2FF',
            },
            gray: {
              foreground: '#FFFFFF',
              DEFAULT: '#686C72',
            },
            default: {
              foreground: '#FFFFFF',
              DEFAULT: '#F5F5F5',
              100: '#FFFFFF',
              200: '#E8E8E8',
              300: '#D2D2D2',
              400: '#BBBBBB',
              500: '#A4A4A4',
              600: '#8E8E8E',
              700: '#777777',
              800: '#606060',
              900: '#4A4A4A',
              1000: '#333333',
            },
            red: {
              foreground: '#FFFFFF',
              DEFAULT: '#D00416',
              100: '#FB3748',
              200: '#D00416',
            },
            // yellow: {
            //   foreground: '#FFFFFF',
            //   DEFAULT: '#E4AE0D',
            //   100: '#F7C83B',
            //   200: '#E4AE0D',
            // },
            // green: {
            //   foreground: '#FFFFFF',
            //   DEFAULT: '#1FC16B',
            //   100: '#84EBB4',
            //   200: '#1FC16B',
            // },
            // lightGray: '#1FC16B',
            subtitle: '#1C1C1C',
          },
        },
      },
    }),
  ],
};
