'use client';

import { Card, CardBody, Image } from '@heroui/react';

export default function RecentSearchesCard() {
  const data = [
    {
      img: 'https://heroui.com/images/hero-card.jpeg',
      heading: 'The Golden Sands of Florida and clifornia ',
      description:
        'Lorem ipsum dolor sit amet, con turadipiscingelit. In sed et donec purus viverra. Sitjusto velit, eu sed',
    },
    {
      img: 'https://heroui.com/images/album-cover.png',
      heading: 'Exploring the Enchanted Forests of Oregon',
      description:
        'Experience the tranquility of towering trees and hidden waterfalls. A journey that centers on mindfulness and appreciation of nature.',
    },
    {
      img: 'https://heroui.com/images/hero-card.jpeg',
      heading: 'Culinary Delights in New Orleans',
      description:
        'Dive into the rich flavors of Creole and Cajun cuisine, as you savor dishes that tell the story of the city`s vibrant culture.',
    },
    {
      img: 'https://heroui.com/images/album-cover.png',
      heading: 'The Historic Streets of Boston',
      description:
        'Walk through history with a blend of modern attractions and historic landmarks, capturing the essence of America`s revolutionary past.',
    },
    {
      img: 'https://heroui.com/images/hero-card.jpeg',
      heading: 'The Urban Wonders of Tokyo',
      description:
        'Navigate the bustling streets and serene temples, discovering the perfect balance between tradition and cutting-edge technology.',
    },
    {
      img: 'https://heroui.com/images/album-cover.png',
      heading: 'The Serene Mountains of Colorado',
      description:
        'Challenge yourself with thrilling outdoor activities amidst breathtaking alpine scenery, perfect for adrenaline seekers.',
    },
  ];

  return (
    <div className="grid grid-cols-3 gap-4 max-md:grid-cols-1">
      {data.map(item => (
        <Card
          key={item.heading}
          isPressable
          shadow="sm"
          onPress={() => {}}
          className="shadow-none rounded-sm w-full"
        >
          <CardBody className="relative overflow-visible p-0">
            {/* Gradient Overlay */}
            {/* <div className="absolute top-0 left-0 w-full h-full z-10 bg-[linear-gradient(180deg,#F29C72_0%,rgba(255,255,255,0)_100%)] pointer-events-none rounded-sm" /> */}

            {/* Image */}
            <Image
              alt={item.img}
              className="min-w-full object-cover h-[220px] shadow-none rounded-bl-none rounded-br-none"
              radius="sm"
              src={item.img}
              width="100%"
            />

            <div className="p-3">
              <p className="text-base font-bold text-start text-[#25233A]">
                {item.heading}
              </p>
              <p className="text-[#868383] text-sm mt-1 leading-6 items-end">
                {item.description}
              </p>
            </div>
          </CardBody>
        </Card>
      ))}
    </div>
  );
}
