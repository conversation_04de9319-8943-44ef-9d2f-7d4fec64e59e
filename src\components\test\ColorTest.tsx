import React from 'react';

const ColorTest = () => {
  return (
    <div className="p-8 space-y-4">
      <h2 className="text-2xl font-bold mb-6">Color Test Component</h2>
      
      {/* Test subtitle color */}
      <div className="space-y-2">
        <h3 className="text-lg font-semibold">Subtitle Color Tests:</h3>
        <p className="text-subtitle">This text should be in subtitle color (#1C1C1C)</p>
        <p className="text-subtitle-foreground">This text should be in subtitle foreground color</p>
        <div className="bg-subtitle text-subtitle-foreground p-4 rounded">
          Background in subtitle color with foreground text
        </div>
      </div>

      {/* Test lightGray color */}
      <div className="space-y-2">
        <h3 className="text-lg font-semibold">Light Gray Color Tests:</h3>
        <p className="text-lightGray">This text should be in lightGray color (#8F9AA8)</p>
        <p className="text-lightGray-foreground">This text should be in lightGray foreground color</p>
        <div className="bg-lightGray text-lightGray-foreground p-4 rounded">
          Background in lightGray color with foreground text
        </div>
      </div>

      {/* Test existing colors for comparison */}
      <div className="space-y-2">
        <h3 className="text-lg font-semibold">Existing Colors (for comparison):</h3>
        <p className="text-primary">Primary color text</p>
        <p className="text-secondary">Secondary color text</p>
        <p className="text-gray">Gray color text</p>
        <div className="bg-primary text-primary-foreground p-2 rounded inline-block mr-2">
          Primary BG
        </div>
        <div className="bg-secondary text-secondary-foreground p-2 rounded inline-block mr-2">
          Secondary BG
        </div>
        <div className="bg-gray text-gray-foreground p-2 rounded inline-block">
          Gray BG
        </div>
      </div>
    </div>
  );
};

export default ColorTest;
