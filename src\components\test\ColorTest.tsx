import React from 'react';

const ColorTest = () => {
  return (
    <div className="p-8 space-y-6">
      <h2 className="text-2xl font-bold mb-6">Complete Color Test Component</h2>

      {/* Primary Color Variants */}
      <div className="space-y-3">
        <h3 className="text-lg font-semibold">Primary Color (#1E1E76):</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-primary text-primary-foreground p-4 rounded">
            bg-primary + text-primary-foreground
          </div>
          <div className="bg-primary-200 text-white p-4 rounded">
            bg-primary-200 (#707FF5)
          </div>
          <div className="text-primary p-4 border rounded">
            text-primary
          </div>
        </div>
      </div>

      {/* Secondary Color */}
      <div className="space-y-3">
        <h3 className="text-lg font-semibold">Secondary Color (#F2F2FF):</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="bg-secondary text-secondary-foreground p-4 rounded border">
            bg-secondary + text-secondary-foreground
          </div>
          <div className="text-secondary p-4 border rounded">
            text-secondary
          </div>
        </div>
      </div>

      {/* Gray Color */}
      <div className="space-y-3">
        <h3 className="text-lg font-semibold">Gray Color (#686C72):</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="bg-gray text-gray-foreground p-4 rounded">
            bg-gray + text-gray-foreground
          </div>
          <div className="text-gray p-4 border rounded">
            text-gray
          </div>
        </div>
      </div>

      {/* Default Color Variants */}
      <div className="space-y-3">
        <h3 className="text-lg font-semibold">Default Color Variants (#F5F5F5):</h3>
        <div className="grid grid-cols-2 md:grid-cols-5 gap-2">
          <div className="bg-default text-default-foreground p-3 rounded text-sm">
            default
          </div>
          <div className="bg-default-100 text-black p-3 rounded text-sm">
            default-100
          </div>
          <div className="bg-default-200 text-black p-3 rounded text-sm">
            default-200
          </div>
          <div className="bg-default-300 text-black p-3 rounded text-sm">
            default-300
          </div>
          <div className="bg-default-400 text-white p-3 rounded text-sm">
            default-400
          </div>
        </div>
        <div className="grid grid-cols-2 md:grid-cols-5 gap-2">
          <div className="bg-default-500 text-white p-3 rounded text-sm">
            default-500
          </div>
          <div className="bg-default-600 text-white p-3 rounded text-sm">
            default-600
          </div>
          <div className="bg-default-700 text-white p-3 rounded text-sm">
            default-700
          </div>
          <div className="bg-default-800 text-white p-3 rounded text-sm">
            default-800
          </div>
          <div className="bg-default-900 text-white p-3 rounded text-sm">
            default-900
          </div>
        </div>
        <div className="grid grid-cols-1 gap-2">
          <div className="bg-default-1000 text-white p-3 rounded text-sm">
            default-1000 (#333333)
          </div>
        </div>
      </div>

      {/* Red Color Variants */}
      <div className="space-y-3">
        <h3 className="text-lg font-semibold">Red Color (#D00416):</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-red text-red-foreground p-4 rounded">
            bg-red + text-red-foreground
          </div>
          <div className="bg-red-100 text-white p-4 rounded">
            bg-red-100 (#FB3748)
          </div>
          <div className="bg-red-200 text-white p-4 rounded">
            bg-red-200 (#D00416)
          </div>
        </div>
      </div>

      {/* Yellow Color Variants */}
      <div className="space-y-3">
        <h3 className="text-lg font-semibold">Yellow Color (#E4AE0D):</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-yellow text-yellow-foreground p-4 rounded">
            bg-yellow + text-yellow-foreground
          </div>
          <div className="bg-yellow-100 text-black p-4 rounded">
            bg-yellow-100 (#F7C83B)
          </div>
          <div className="bg-yellow-200 text-white p-4 rounded">
            bg-yellow-200 (#E4AE0D)
          </div>
        </div>
      </div>

      {/* Green Color Variants */}
      <div className="space-y-3">
        <h3 className="text-lg font-semibold">Green Color (#1FC16B):</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-green text-green-foreground p-4 rounded">
            bg-green + text-green-foreground
          </div>
          <div className="bg-green-100 text-black p-4 rounded">
            bg-green-100 (#84EBB4)
          </div>
          <div className="bg-green-200 text-white p-4 rounded">
            bg-green-200 (#1FC16B)
          </div>
        </div>
      </div>

      {/* Light Gray Color */}
      <div className="space-y-3">
        <h3 className="text-lg font-semibold">Light Gray Color (#8F9AA8):</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="bg-lightGray text-lightGray-foreground p-4 rounded">
            bg-lightGray + text-lightGray-foreground
          </div>
          <div className="text-lightGray p-4 border rounded">
            text-lightGray
          </div>
        </div>
      </div>

      {/* Subtitle Color */}
      <div className="space-y-3">
        <h3 className="text-lg font-semibold">Subtitle Color (#1C1C1C):</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="bg-subtitle text-subtitle-foreground p-4 rounded">
            bg-subtitle + text-subtitle-foreground
          </div>
          <div className="text-subtitle p-4 border rounded">
            text-subtitle
          </div>
        </div>
      </div>

      {/* Usage Examples */}
      <div className="space-y-3">
        <h3 className="text-lg font-semibold">Real Usage Examples:</h3>
        <div className="space-y-4">
          <div className="p-4 border rounded">
            <h4 className="text-primary font-semibold mb-2">Card Title</h4>
            <p className="text-subtitle mb-3">This is a subtitle using the custom subtitle color</p>
            <p className="text-lightGray text-sm">Additional info in light gray</p>
            <button className="bg-primary text-primary-foreground px-4 py-2 rounded mt-3 hover:opacity-90">
              Primary Button
            </button>
          </div>

          <div className="bg-default-100 p-4 rounded">
            <h4 className="text-default-900 font-semibold mb-2">Light Background Card</h4>
            <p className="text-default-700">Content with default color variants</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ColorTest;
