'use client';

import { useState, useEffect } from 'react';

import DiscoverPage from '@/screen/Discover';

export default function Discover() {
  const [isLoading, setIsLoading] = useState(true);

  // Simulate loading time - replace with actual data fetching logic
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 2000); // Show skeleton for 2 seconds

    return () => clearTimeout(timer);
  }, []);

  return (
    <div>
      <DiscoverPage isLoading={isLoading} />
    </div>
  );
}
