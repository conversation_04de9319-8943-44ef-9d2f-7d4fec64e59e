'use client';

import { Tabs, Tab } from '@heroui/react';
import { useState } from 'react';

import Itinerary from './Itinerary';
import Transport from './Transport';
import Hotel from './Hotel';
import Activities from './Activities';
import Calendar from './Calendar';

export default function TabsItinerary() {
  const [selectedTab, setSelectedTab] = useState('itinerary');

  return (
    <div className="flex w-full flex-col">
      <Tabs
        aria-label="Options"
        selectedKey={selectedTab}
        onSelectionChange={key => setSelectedTab(String(key))}
        classNames={{
          tabList: 'w-full flex flex-row justify-start gap-6',
          tab: `
            relative h-12 px-1 flex items-end justify-start
            group-data-[selected=true]:gradient-border
          `,
          tabContent: `
            pb-3 text-sm text-gray-600 text-left border-red-300
            group-data-[selected=true]:text-black
            group-data-[selected=true]:font-bold
          `,
        }}
        variant="underlined"
      >
        <Tab key="itinerary" title="Itinerary" />
        <Tab key="transport" title="Transport" />
        <Tab key="hotel" title="Hotel" />
        <Tab key="activities" title="Activities" />
        <Tab key="calendar" title="Calendar" />
      </Tabs>

      <div className="mt-4 text-sm text-gray-700">
        {selectedTab === 'itinerary' && <Itinerary />}
        {selectedTab === 'transport' && <Transport />}
        {selectedTab === 'hotel' && <Hotel />}
        {selectedTab === 'activities' && <Activities />}
        {selectedTab === 'calendar' && <Calendar />}
      </div>
    </div>
  );
}
