'use client';

import { Card, CardBody, Image } from '@heroui/react';

interface MediaCardProps {
  item: {
    img: string;
    heading: string;
  };
}

export default function MediaCardComp({ item }: MediaCardProps) {
  return (
    <Card
      isPressable
      shadow="sm"
      onPress={() => {}}
      className="shadow-none rounded-sm bg-transparent w-full"
    >
      <CardBody className="relative overflow-visible p-0">
        {/* Gradient Overlay */}
        {/* <div className="absolute top-0 left-0 w-full h-full z-10 bg-[linear-gradient(180deg,#F29C72_0%,rgba(255,255,255,0)_100%)] pointer-events-none rounded-sm" /> */}

        {/* Image */}
        <Image
          alt={item.img}
          className="min-w-full object-cover h-[220px] shadow-none"
          radius="sm"
          src={item.img}
          width="100%"
        />

        <div className="mt-1">
          <p className="text-base font-bold text-start text-[#25233A]">
            {item.heading}
          </p>
          <p className="text-[#868383] text-sm -mt-0.5">{item.heading}</p>
        </div>
      </CardBody>
    </Card>
  );
}
